using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces
{
    /// <summary>
    /// Interface for RZW Savings Plan Service operations
    /// </summary>
    public interface IRzwSavingsPlanService
    {
        /// <summary>
        /// Gets all active savings plans
        /// </summary>
        /// <returns>List of active savings plans</returns>
        Task<List<RzwSavingsPlan>> GetActivePlansAsync();

        /// <summary>
        /// Gets a savings plan by ID
        /// </summary>
        /// <param name="planId">The plan ID</param>
        /// <returns>The savings plan if found</returns>
        Task<RzwSavingsPlan?> GetPlanByIdAsync(int planId);

        /// <summary>
        /// Validates if a plan exists and is active
        /// </summary>
        /// <param name="planId">The plan ID</param>
        /// <returns>True if plan is valid and active</returns>
        Task<bool> IsPlanValidAsync(int planId);

        /// <summary>
        /// Validates if an amount meets the plan requirements
        /// </summary>
        /// <param name="planId">The plan ID</param>
        /// <param name="amount">The amount to validate</param>
        /// <returns>True if amount meets plan requirements</returns>
        Task<bool> ValidatePlanAsync(int planId, decimal amount);

        /// <summary>
        /// Gets all plans (including inactive ones)
        /// </summary>
        /// <returns>List of all savings plans</returns>
        Task<List<RzwSavingsPlan>> GetAllPlansAsync();

        /// <summary>
        /// Creates a new savings plan
        /// </summary>
        /// <param name="plan">The plan to create</param>
        /// <returns>The created plan</returns>
        Task<RzwSavingsPlan> CreatePlanAsync(RzwSavingsPlan plan);

        /// <summary>
        /// Updates an existing savings plan
        /// </summary>
        /// <param name="plan">The plan to update</param>
        /// <returns>The updated plan</returns>
        Task<RzwSavingsPlan> UpdatePlanAsync(RzwSavingsPlan plan);

        /// <summary>
        /// Deactivates a savings plan
        /// </summary>
        /// <param name="planId">The plan ID to deactivate</param>
        /// <returns>True if successful</returns>
        Task<bool> DeactivatePlanAsync(int planId);

        /// <summary>
        /// Gets plans that are eligible for early withdrawal based on held days
        /// </summary>
        /// <param name="heldDays">Number of days the account has been held</param>
        /// <returns>List of eligible plans ordered by term duration descending</returns>
        Task<List<RzwSavingsPlan>> GetEligiblePlansForEarlyWithdrawalAsync(int heldDays);

        /// <summary>
        /// Deletes a savings plan
        /// </summary>
        /// <param name="planId">The plan ID to delete</param>
        /// <returns>True if deleted, false if not found</returns>
        Task<bool> DeletePlanAsync(int planId);

        /// <summary>
        /// Gets statistics for a specific plan
        /// </summary>
        /// <param name="planId">The plan ID</param>
        /// <returns>Plan statistics</returns>
        Task<(int TotalAccounts, decimal TotalLockedRzw, decimal TotalInterestPaid)> GetPlanStatisticsAsync(int planId);
    }
}
